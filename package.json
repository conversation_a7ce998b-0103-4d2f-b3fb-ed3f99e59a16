{"name": "iraqi-politician-website", "version": "1.0.0", "description": "Full-stack website for Iraqi politician with admin dashboard", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:admin\"", "dev:frontend": "cd apps/frontend && npm run dev", "dev:backend": "cd apps/backend && npm run dev", "dev:admin": "cd apps/admin && npm run dev", "build": "npm run build:frontend && npm run build:backend && npm run build:admin", "build:frontend": "cd apps/frontend && npm run build", "build:backend": "cd apps/backend && npm run build", "build:admin": "cd apps/admin && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\" \"npm run start:admin\"", "start:frontend": "cd apps/frontend && npm run start", "start:backend": "cd apps/backend && npm run start", "start:admin": "cd apps/admin && npm run start", "lint": "npm run lint:frontend && npm run lint:backend && npm run lint:admin", "lint:frontend": "cd apps/frontend && npm run lint", "lint:backend": "cd apps/backend && npm run lint", "lint:admin": "cd apps/admin && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "Iraqi Politician Website Team", "license": "MIT"}