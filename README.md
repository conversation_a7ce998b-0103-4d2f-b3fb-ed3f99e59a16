# 🇮🇶 موقع السياسي العراقي | Iraqi Politician Website

موقع إلكتروني كامل للسياسي العراقي مع لوحة تحكم إدارية، مبني بأحدث التقنيات ومجهز للنشر الفوري.

A complete website for Iraqi politicians with an administrative dashboard, built with the latest technologies and ready for immediate deployment.

## ✨ المميزات | Features

### 🌐 الواجهة الأمامية | Frontend
- **Next.js 14** مع App Router
- **دعم كامل للغتين** العربية والإنجليزية مع RTL
- **تصميم متجاوب** لجميع الأجهزة
- **الوضع الليلي** Dark/Light mode
- **تحسين محركات البحث** SEO optimized
- **أداء عالي** مع تحسينات الصور والتحميل

### 🔧 الواجهة الخلفية | Backend
- **Node.js + Express.js** API
- **MongoDB** مع Mongoose ODM
- **JWT Authentication** نظام مصادقة آمن
- **Rate Limiting** حماية من الهجمات
- **File Upload** رفع الملفات والصور
- **Email Integration** إرسال الإيميلات

### 🛡️ الأمان | Security
- **Helmet.js** حماية HTTP headers
- **CORS** تكوين آمن
- **Input Validation** التحقق من المدخلات
- **XSS Protection** حماية من XSS
- **Rate Limiting** تحديد معدل الطلبات

### 📱 الصفحات | Pages
- **الصفحة الرئيسية** مع صورة السياسي ورسالة ترحيب
- **السيرة الذاتية** مع الإنجازات
- **الأخبار** مع نظام بحث وتصفية
- **البيانات الصحفية** مع تحميل PDF
- **وسائل التواصل** روابط السوشيال ميديا
- **تواصل معنا** نموذج تواصل مفعّل

### 🎛️ لوحة التحكم | Admin Dashboard
- **تسجيل دخول آمن** للمسؤولين
- **إدارة الأخبار** إضافة وتعديل وحذف
- **إدارة البيانات الصحفية** رفع ملفات PDF
- **إدارة الرسائل** عرض والرد على الرسائل
- **الإعدادات العامة** تخصيص الموقع

## 🚀 التثبيت والتشغيل | Installation & Setup

### المتطلبات | Prerequisites
- Node.js 18+ 
- MongoDB
- npm أو yarn

### 1. استنساخ المشروع | Clone Repository
```bash
git clone <repository-url>
cd iraqi-politician-website
```

### 2. تثبيت التبعيات | Install Dependencies
```bash
# تثبيت التبعيات الرئيسية
npm install

# تثبيت تبعيات الواجهة الأمامية
cd apps/frontend && npm install

# تثبيت تبعيات الواجهة الخلفية
cd ../backend && npm install

# تثبيت تبعيات لوحة التحكم
cd ../admin && npm install
```

### 3. إعداد قاعدة البيانات | Database Setup
```bash
# تشغيل MongoDB
mongod

# أو باستخدام Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

### 4. إعداد متغيرات البيئة | Environment Variables

#### Backend (.env)
```bash
cd apps/backend
cp .env.example .env
# قم بتعديل الملف وإضافة القيم المطلوبة
```

#### Frontend (.env.local)
```bash
cd apps/frontend
cp .env.example .env.local
# قم بتعديل الملف وإضافة القيم المطلوبة
```

### 5. إنشاء المستخدم الأول | Create Admin User
```bash
cd apps/backend
npm run create-admin
```

### 6. تشغيل التطبيق | Run Application
```bash
# تشغيل جميع التطبيقات
npm run dev

# أو تشغيل كل تطبيق منفصل
npm run dev:frontend  # http://localhost:3000
npm run dev:backend   # http://localhost:5000
npm run dev:admin     # http://localhost:3001
```

## 📁 هيكل المشروع | Project Structure

```
iraqi-politician-website/
├── apps/
│   ├── frontend/          # Next.js Frontend
│   │   ├── src/
│   │   │   ├── app/       # App Router pages
│   │   │   ├── components/ # React components
│   │   │   ├── i18n/      # Internationalization
│   │   │   └── lib/       # Utilities
│   │   └── public/        # Static assets
│   │
│   ├── backend/           # Express.js API
│   │   ├── src/
│   │   │   ├── models/    # MongoDB models
│   │   │   ├── routes/    # API routes
│   │   │   ├── middleware/ # Express middleware
│   │   │   ├── utils/     # Utilities
│   │   │   └── scripts/   # Setup scripts
│   │   └── uploads/       # File uploads
│   │
│   └── admin/             # Admin Dashboard
│       ├── src/
│       │   ├── app/       # Admin pages
│       │   ├── components/ # Admin components
│       │   └── lib/       # Admin utilities
│       └── public/        # Admin assets
│
├── packages/
│   └── shared/            # Shared utilities
│
└── docs/                  # Documentation
```

## 🌐 النشر | Deployment

### Frontend (Vercel)
```bash
cd apps/frontend
vercel --prod
```

### Backend (Railway/Render)
```bash
cd apps/backend
# اتبع تعليمات النشر الخاصة بالمنصة المختارة
```

### متغيرات البيئة للإنتاج | Production Environment Variables
تأكد من إعداد جميع متغيرات البيئة في منصة النشر:
- `MONGODB_URI`
- `JWT_SECRET`
- `EMAIL_*` متغيرات الإيميل
- `CLOUDINARY_*` متغيرات رفع الملفات

## 🔧 التخصيص | Customization

### تغيير المعلومات الأساسية | Basic Information
1. سجل دخول إلى لوحة التحكم
2. اذهب إلى الإعدادات
3. قم بتحديث:
   - اسم السياسي
   - المنصب
   - السيرة الذاتية
   - معلومات التواصل
   - روابط وسائل التواصل

### إضافة المحتوى | Adding Content
1. **الأخبار**: اذهب إلى قسم الأخبار في لوحة التحكم
2. **البيانات الصحفية**: ارفع ملفات PDF في قسم البيانات
3. **الصور**: استخدم نظام رفع الملفات المدمج

## 🛠️ التقنيات المستخدمة | Technologies Used

### Frontend
- **Next.js 14** - React framework
- **TailwindCSS** - Styling
- **next-intl** - Internationalization
- **SWR** - Data fetching
- **React Hook Form** - Form handling
- **Zod** - Validation

### Backend
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **JWT** - Authentication
- **Multer** - File uploads
- **Nodemailer** - Email sending

### DevOps
- **TypeScript** - Type safety
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 📞 الدعم | Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة | Contributing

نرحب بالمساهمات! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.

---

**تم تطويره بـ ❤️ للسياسيين العراقيين**

**Developed with ❤️ for Iraqi Politicians**
